"""
Configuration settings for VoxDiscover Backend
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings"""

    # Environment
    ENVIRONMENT: str = "development"
    DEBUG: bool = True

    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "VoxDiscover"

    # Security
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:5173"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]

    # Supabase Configuration
    SUPABASE_URL: str
    SUPABASE_ANON_KEY: str
    SUPABASE_SERVICE_ROLE_KEY: str
    SUPABASE_KEY: Optional[str] = None  # Legacy key name

    # Daily.co Configuration
    DAILY_API_KEY: str
    DAILY_API_URL: str = "https://api.daily.co/v1"
    DAILY_SAMPLE_ROOM_URL: Optional[str] = None

    # PAMS Configuration
    PAMS_URL: str = "http://localhost:8001"
    PAMS_API_KEY: str

    # Redis Configuration
    REDIS_URL: str = "redis://localhost:6379"

    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_BURST: int = 100

    # AI Provider API Keys
    OPENAI_API_KEY: Optional[str] = None
    ANTHROPIC_API_KEY: Optional[str] = None
    GOOGLE_API_KEY: Optional[str] = None
    GROQ_API_KEY: Optional[str] = None
    DEEPSEEK_API_KEY: Optional[str] = None

    # STT Provider API Keys
    DEEPGRAM_API_KEY: Optional[str] = None
    AZURE_SPEECH_KEY: Optional[str] = None
    AZURE_API_KEY: Optional[str] = None
    AZURE_REGION: Optional[str] = None
    WHISPER_MODEL_SIZE: Optional[str] = None

    # TTS Provider API Keys
    ELEVENLABS_API_KEY: Optional[str] = None
    CARTESIA_API_KEY: Optional[str] = None

    # Session Configuration
    MAX_SESSION_DURATION_MINUTES: int = 60
    MAX_CONCURRENT_SESSIONS_PER_USER: int = 5

    # Cost Configuration
    DEFAULT_COST_PER_MINUTE: float = 0.10

    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"

    # Monitoring
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090

    model_config = {
        "env_file": ".env",
        "case_sensitive": True,
        "extra": "ignore"
    }


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    return Settings()


# Global settings instance
settings = get_settings()


# Validation
def validate_settings():
    """Validate required settings"""
    required_settings = [
        "SECRET_KEY",
        "SUPABASE_URL",
        "SUPABASE_ANON_KEY",
        "SUPABASE_SERVICE_ROLE_KEY",
        "DAILY_API_KEY",
        "PAMS_API_KEY"
    ]

    missing = []
    for setting in required_settings:
        if not getattr(settings, setting, None):
            missing.append(setting)

    if missing:
        raise ValueError(f"Missing required environment variables: {', '.join(missing)}")


# Validate on import
validate_settings()
