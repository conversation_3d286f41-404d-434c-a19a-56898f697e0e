"""
VoxDiscover Backend - Main FastAPI Application
Voice AI Agent Platform with Daily.co, Pipecat, and Supabase
"""

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from contextlib import asynccontextmanager
import uvicorn
import logging

from backend.core.config import settings
from backend.core.database import init_db
from backend.api.v1 import agents, sessions, analytics, calls
from backend.core.logging_config import setup_logging
from backend.api.v1.calls import CallRequest

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)



@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting VoxDiscover Backend...")
    await init_db()
    logger.info("Database initialized")

    yield

    # Shutdown
    logger.info("Shutting down VoxDiscover Backend...")

# Create FastAPI app
app = FastAPI(
    title="VoxDiscover API",
    description="Voice AI Agent Platform - VAPI-like system with Daily.co, Pipecat, and Supabase",
    version="1.0.0",
    docs_url="/docs" if settings.ENVIRONMENT != "production" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT != "production" else None,
    lifespan=lifespan
)

# Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "voxdiscover-backend",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }



# Include API routers
app.include_router(
    agents.router,
    prefix="/api/v1/agents",
    tags=["agents"]
)

app.include_router(
    sessions.router,
    prefix="/api/v1/sessions",
    tags=["sessions"]
)

app.include_router(
    analytics.router,
    prefix="/api/v1/analytics",
    tags=["analytics"]
)

app.include_router(
    calls.router,
    prefix="/api/v1",
    tags=["calls"]
)

# RTVI Connect endpoint (root level for RTVI compatibility)
@app.post("/connect")
async def rtvi_connect_root(request: Request):
    """RTVI connect endpoint at root level for compatibility"""
    try:
        # Parse the request body
        body = await request.json()

        # Extract agent_id from the request
        agent_id = body.get("agent_id")
        if not agent_id:
            raise HTTPException(status_code=400, detail="agent_id is required")

        # For testing, create a simple Daily room without database dependency
        from backend.services.daily_service import DailyService
        import uuid

        # Generate unique call ID
        call_id = str(uuid.uuid4())

        # Create Daily room
        daily_service = DailyService()
        room_name = f"test-call-{call_id[:8]}"

        room_data = await daily_service.create_room(
            room_name=room_name,
            expiry_minutes=30,
            max_participants=2
        )

        if not room_data:
            raise HTTPException(
                status_code=500,
                detail="Failed to create room"
            )

        room_url = room_data["url"]

        # Create token for user
        token = await daily_service.create_token(
            room_name=room_name,
            user_name="test-user",
            expiry_minutes=30
        )

        if not token:
            raise HTTPException(
                status_code=500,
                detail="Failed to create token"
            )

        logger.info(f"Created test room {room_url} for agent {agent_id}")

        # Return the authentication bundle in format expected by DailyTransport
        return {"room_url": room_url, "token": token}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in RTVI connect: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "VoxDiscover Voice AI Agent Platform",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    uvicorn.run(
        "backend.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.ENVIRONMENT == "development",
        log_level="info"
    )
