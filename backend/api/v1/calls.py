"""
Call management API endpoints for voice agent sessions
"""

import asyncio
import os
import subprocess
import uuid
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
import aiohttp
from loguru import logger

from backend.core.dependencies import get_current_user
from backend.core.database import get_db_manager
from backend.models.sessions import SessionBase, SessionStatus
from backend.services.daily_service import DailyService

router = APIRouter(prefix="/calls", tags=["calls"])

# Store active call processes: {call_id: (process, room_url, agent_id)}
active_calls: Dict[str, tuple] = {}

class CallRequest(BaseModel):
    agent_id: str

class CallResponse(BaseModel):
    call_id: str
    room_url: str
    token: str
    status: str

class CallStatus(BaseModel):
    call_id: str
    status: str
    room_url: Optional[str] = None
    duration_seconds: Optional[int] = None
    participant_count: Optional[int] = None

@router.post("/start", response_model=CallResponse)
async def start_call(
    request: CallRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Start a new voice call with an agent"""
    try:
        db_manager = get_db_manager()

        # Get agent details
        agent = await db_manager.get_agent(request.agent_id, current_user["id"])
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        if not agent.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Agent is not active"
            )

        # Create Daily room
        daily_service = DailyService()
        room_name = f"agent-call-{uuid.uuid4().hex[:8]}"
        room_data = await daily_service.create_room(room_name, expiry_minutes=60)

        if not room_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create Daily room"
            )

        room_url = room_data["url"]

        # Generate token for the user
        token = await daily_service.create_token(room_name, current_user.get("email", "User"))

        # Generate unique call ID
        call_id = str(uuid.uuid4())

        # Create session record
        from datetime import datetime
        session_data = {
            "agent_id": str(request.agent_id),
            "user_id": current_user["id"],
            "daily_room_url": room_url,
            "daily_room_name": room_name,
            "status": SessionStatus.CONNECTING.value,
            "participant_count": 0,
            "duration_seconds": 0,
            "started_at": datetime.utcnow().isoformat(),
            "cost_usd": 0.0
        }

        session = await db_manager.create_session(session_data)

        # Start the bot process
        bot_process = await _start_bot_process(agent, room_url, token)

        # Store the active call
        active_calls[call_id] = (bot_process, room_url, request.agent_id, session["id"])

        logger.info(f"Started call {call_id} for agent {request.agent_id} in room {room_url}")

        return CallResponse(
            call_id=call_id,
            room_url=room_url,
            token=token,
            status="connecting"
        )

    except Exception as e:
        logger.error(f"Failed to start call: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start call: {str(e)}"
        )

@router.post("/end/{call_id}")
async def end_call(
    call_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """End an active call"""
    try:
        if call_id not in active_calls:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Call not found"
            )

        process, room_url, agent_id, session_id = active_calls[call_id]

        # Terminate the bot process
        if process and process.poll() is None:
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()

        # Update session status
        db_manager = get_db_manager()
        await db_manager.update_session_status(session_id, SessionStatus.ENDED)

        # Clean up Daily room
        daily_service = DailyService()
        room_name = room_url.split("/")[-1]
        await daily_service.delete_room(room_name)

        # Remove from active calls
        del active_calls[call_id]

        logger.info(f"Ended call {call_id}")

        return {"status": "ended", "call_id": call_id}

    except Exception as e:
        logger.error(f"Failed to end call: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to end call: {str(e)}"
        )

@router.get("/status/{call_id}", response_model=CallStatus)
async def get_call_status(
    call_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get the status of an active call"""
    try:
        if call_id not in active_calls:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Call not found"
            )

        process, room_url, agent_id, session_id = active_calls[call_id]

        # Check if process is still running
        if process.poll() is None:
            status_str = "active"
        else:
            status_str = "ended"
            # Clean up if process ended
            del active_calls[call_id]

        # Get session details
        db_manager = get_db_manager()
        session = await db_manager.get_session(session_id)

        return CallStatus(
            call_id=call_id,
            status=status_str,
            room_url=room_url,
            duration_seconds=session.duration_seconds if session else 0,
            participant_count=session.participant_count if session else 0
        )

    except Exception as e:
        logger.error(f"Failed to get call status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get call status: {str(e)}"
        )

@router.get("/active")
async def get_active_calls(
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """Get all active calls for the current user"""
    try:
        # Filter active calls and check process status
        user_calls = []
        calls_to_remove = []

        for call_id, (process, room_url, agent_id, session_id) in active_calls.items():
            # Check if process is still running
            if process.poll() is not None:
                calls_to_remove.append(call_id)
                continue

            # Get session to check if it belongs to current user
            db_manager = get_db_manager()
            session = await db_manager.get_session(session_id)

            if session and session.user_id == current_user["id"]:
                user_calls.append({
                    "call_id": call_id,
                    "agent_id": agent_id,
                    "room_url": room_url,
                    "status": "active",
                    "duration_seconds": session.duration_seconds
                })

        # Clean up ended calls
        for call_id in calls_to_remove:
            del active_calls[call_id]

        return {"active_calls": user_calls}

    except Exception as e:
        logger.error(f"Failed to get active calls: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get active calls: {str(e)}"
        )

@router.post("/connect")
async def rtvi_connect(
    request: CallRequest,
    current_user: Dict[str, Any] = Depends(get_current_user)
):
    """RTVI connect endpoint for client connections"""
    try:
        db_manager = get_db_manager()

        # Get agent details
        agent = await db_manager.get_agent(request.agent_id, current_user["id"])
        if not agent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Agent not found"
            )

        if not agent.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Agent is not active"
            )

        # Create Daily room
        daily_service = DailyService()
        room_name = f"agent-call-{uuid.uuid4().hex[:8]}"
        room_data = await daily_service.create_room(room_name, expiry_minutes=60)

        if not room_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create Daily room"
            )

        room_url = room_data["url"]

        # Generate token for the user
        token = await daily_service.create_token(room_name, current_user.get("email", "User"))

        # Start the bot process
        bot_process = await _start_bot_process(agent, room_url, token)

        # Generate unique call ID
        call_id = str(uuid.uuid4())

        # Create session record
        session_data = {
            "agent_id": str(request.agent_id),
            "user_id": current_user["id"],
            "daily_room_url": room_url,
            "daily_room_name": room_name,
            "status": SessionStatus.CONNECTING.value,
            "participant_count": 0,
            "duration_seconds": 0,
            "started_at": datetime.utcnow().isoformat(),
            "cost_usd": 0.0
        }

        session = await db_manager.create_session(session_data)

        # Store the active call
        active_calls[call_id] = (bot_process, room_url, request.agent_id, session["id"])

        logger.info(f"RTVI connect for agent {request.agent_id} in room {room_url}")

        # Return the authentication bundle in format expected by DailyTransport
        return {"room_url": room_url, "token": token}

    except Exception as e:
        logger.error(f"Failed to connect via RTVI: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to connect: {str(e)}"
        )

async def _start_bot_process(agent, room_url: str, token: str) -> subprocess.Popen:
    """Start a bot process for the given agent configuration"""
    try:
        # Create a temporary bot script based on agent configuration
        bot_script = _generate_bot_script(agent)

        # Write the script to a temporary file
        script_path = f"/tmp/bot_{agent['id']}_{uuid.uuid4().hex[:8]}.py"
        with open(script_path, 'w') as f:
            f.write(bot_script)

        # Start the bot process
        process = subprocess.Popen(
            [f"python3 {script_path} -u {room_url} -t {token}"],
            shell=True,
            bufsize=1,
            cwd=os.path.dirname(os.path.abspath(__file__)),
        )

        return process

    except Exception as e:
        logger.error(f"Failed to start bot process: {e}")
        raise

def _generate_bot_script(agent) -> str:
    """Generate a bot script based on agent configuration"""
    # This is a simplified version - in production, you'd want a more robust template system
    agent_name = agent.get('name', 'Agent')
    llm_model = agent.get('llm_model', 'gpt-3.5-turbo')
    llm_temperature = agent.get('llm_temperature', 0.7)
    tts_voice_id = agent.get('tts_voice_id', 'pNInz6obpgDQGcFmaJgB')
    system_prompt = agent.get('system_prompt', 'You are a helpful AI assistant.')

    return f'''
import asyncio
import os
import sys
import argparse
from pipecat.transports.services.daily import DailyTransport, DailyParams
from pipecat.services.openai.llm import OpenAILLMService
from pipecat.services.elevenlabs.tts import ElevenLabsTTSService
from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import PipelineRunner
from pipecat.pipeline.task import PipelineTask, PipelineParams
from pipecat.processors.aggregators.openai_llm_context import OpenAILLMContext

async def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-u", "--url", required=True, help="Room URL")
    parser.add_argument("-t", "--token", required=True, help="Token")
    args = parser.parse_args()

    transport = DailyTransport(
        args.url,
        args.token,
        "{agent_name}",
        DailyParams(
            audio_in_enabled=True,
            audio_out_enabled=True,
            transcription_enabled=True,
        ),
    )

    # Configure LLM
    llm = OpenAILLMService(
        api_key=os.getenv("OPENAI_API_KEY"),
        model="{llm_model}",
        temperature={llm_temperature}
    )

    # Configure TTS
    tts = ElevenLabsTTSService(
        api_key=os.getenv("ELEVENLABS_API_KEY"),
        voice_id="{tts_voice_id}"
    )

    messages = [
        {{
            "role": "system",
            "content": """{system_prompt}"""
        }}
    ]

    context = OpenAILLMContext(messages)
    context_aggregator = llm.create_context_aggregator(context)

    pipeline = Pipeline([
        transport.input(),
        context_aggregator.user(),
        llm,
        tts,
        transport.output(),
        context_aggregator.assistant(),
    ])

    task = PipelineTask(
        pipeline,
        params=PipelineParams(
            allow_interruptions=True,
            enable_metrics=True,
        ),
    )

    @transport.event_handler("on_first_participant_joined")
    async def on_first_participant_joined(transport, participant):
        await transport.capture_participant_transcription(participant["id"])
        await task.queue_frames([context_aggregator.user().get_context_frame()])

    @transport.event_handler("on_participant_left")
    async def on_participant_left(transport, participant, reason):
        await task.cancel()

    runner = PipelineRunner()
    await runner.run(task)

if __name__ == "__main__":
    asyncio.run(main())
'''

# Cleanup function for server shutdown
async def cleanup_calls():
    """Clean up all active calls on server shutdown"""
    for call_id, (process, room_url, agent_id, session_id) in active_calls.items():
        if process and process.poll() is None:
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()

    active_calls.clear()
