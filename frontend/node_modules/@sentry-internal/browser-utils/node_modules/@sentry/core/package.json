{"name": "@sentry/core", "version": "8.55.0", "description": "Base implementation for all Sentry JavaScript SDKs", "repository": "git://github.com/getsentry/sentry-javascript.git", "homepage": "https://github.com/getsentry/sentry-javascript/tree/master/packages/core", "author": "Sentry", "license": "MIT", "engines": {"node": ">=14.18"}, "files": ["/build"], "main": "build/cjs/index.js", "module": "build/esm/index.js", "types": "build/types/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./build/types/index.d.ts", "default": "./build/esm/index.js"}, "require": {"types": "./build/types/index.d.ts", "default": "./build/cjs/index.js"}}}, "typesVersions": {"<4.9": {"build/types/index.d.ts": ["build/types-ts3.8/index.d.ts"]}}, "publishConfig": {"access": "public", "tag": "v8"}, "TODO(v9):": "Remove these dependencies", "devDependencies": {"@types/array.prototype.flat": "^1.2.1", "array.prototype.flat": "^1.3.0", "zod": "^3.24.1"}, "scripts": {"build": "run-p build:transpile build:types", "build:dev": "yarn build", "build:transpile": "rollup -c rollup.npm.config.mjs", "build:types": "run-s build:types:core build:types:downlevel", "build:types:core": "tsc -p tsconfig.types.json", "build:types:downlevel": "yarn downlevel-dts build/types build/types-ts3.8 --to ts3.8", "build:watch": "run-p build:transpile:watch build:types:watch", "build:dev:watch": "yarn build:watch", "build:transpile:watch": "rollup -c rollup.npm.config.mjs --watch", "build:types:watch": "tsc -p tsconfig.types.json --watch", "build:tarball": "npm pack", "circularDepCheck": "madge --circular src/index.ts", "clean": "rimraf build coverage sentry-core-*.tgz", "fix": "eslint . --format stylish --fix", "lint": "eslint . --format stylish", "test": "jest", "test:watch": "jest --watch", "yalc:publish": "yalc publish --push --sig"}, "volta": {"extends": "../../package.json"}, "sideEffects": false}