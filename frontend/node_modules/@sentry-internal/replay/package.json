{"name": "@sentry-internal/replay", "version": "8.55.0", "description": "User replays for Sentry", "main": "build/npm/cjs/index.js", "module": "build/npm/esm/index.js", "types": "build/npm/types/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./build/npm/types/index.d.ts", "default": "./build/npm/esm/index.js"}, "require": {"types": "./build/npm/types/index.d.ts", "default": "./build/npm/cjs/index.js"}}}, "typesVersions": {"<4.9": {"build/npm/types/index.d.ts": ["build/npm/types-ts3.8/index.d.ts"]}}, "files": ["/build/npm"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"build": "run-p build:transpile build:types build:bundle", "build:transpile": "rollup -c rollup.npm.config.mjs", "build:bundle": "rollup -c rollup.bundle.config.mjs", "build:dev": "run-p build:transpile build:types", "build:types": "run-s build:types:core build:types:downlevel", "build:types:core": "tsc -p tsconfig.types.json", "build:types:downlevel": "yarn downlevel-dts build/npm/types build/npm/types-ts3.8 --to ts3.8", "build:watch": "run-p build:transpile:watch build:bundle:watch build:types:watch", "build:dev:watch": "run-p build:transpile:watch build:types:watch", "build:transpile:watch": "yarn build:transpile --watch", "build:bundle:watch": "yarn build:bundle --watch", "build:types:watch": "tsc -p tsconfig.types.json --watch", "build:tarball": "npm pack", "circularDepCheck": "madge --circular src/index.ts", "clean": "rimraf build sentry-replay-*.tgz", "fix": "run-s fix:biome fix:eslint", "fix:eslint": "eslint . --format stylish --fix", "fix:biome": "biome check --apply .", "lint": "eslint . --format stylish", "test": "vitest run", "test:watch": "vitest --watch", "yalc:publish": "yalc publish --push --sig"}, "repository": {"type": "git", "url": "git+https://github.com/getsentry/sentry-javascript.git"}, "author": "Sentry", "license": "MIT", "bugs": {"url": "https://github.com/getsentry/sentry-javascript/issues"}, "homepage": "https://docs.sentry.io/platforms/javascript/session-replay/", "devDependencies": {"@babel/core": "^7.17.5", "@sentry-internal/replay-worker": "8.55.0", "@sentry-internal/rrweb": "2.31.0", "@sentry-internal/rrweb-snapshot": "2.31.0", "fflate": "^0.8.1", "jest-matcher-utils": "^29.0.0", "jsdom-worker": "^0.2.1"}, "dependencies": {"@sentry-internal/browser-utils": "8.55.0", "@sentry/core": "8.55.0"}, "engines": {"node": ">=14.18"}, "volta": {"extends": "../../package.json"}}