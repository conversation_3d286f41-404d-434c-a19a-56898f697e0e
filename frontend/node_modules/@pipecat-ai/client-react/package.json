{"name": "@pipecat-ai/client-react", "version": "0.3.5", "license": "BSD-2-<PERSON><PERSON>", "main": "dist/index.js", "module": "dist/index.module.js", "types": "dist/index.d.ts", "source": "src/index.ts", "repository": {"type": "git", "url": "git+https://github.com/pipecat-ai/pipecat-client-web.git"}, "files": ["dist", "package.json", "README.md"], "scripts": {"build": "parcel build --no-cache", "dev": "parcel watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "devDependencies": {"@pipecat-ai/client-js": "*", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "eslint": "^9.11.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-simple-import-sort": "^12.1.1", "parcel": "^2.12.0", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.2.2"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18", "@pipecat-ai/client-js": "*"}, "dependencies": {"jotai": "^2.9.0"}}