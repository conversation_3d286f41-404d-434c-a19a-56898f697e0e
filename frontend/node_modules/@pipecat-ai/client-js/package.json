{"name": "@pipecat-ai/client-js", "version": "0.3.5", "license": "BSD-2-<PERSON><PERSON>", "main": "dist/index.js", "module": "dist/index.module.js", "types": "dist/index.d.ts", "source": "src/index.ts", "repository": {"type": "git", "url": "git+https://github.com/pipecat-ai/pipecat-client-web.git"}, "files": ["dist", "package.json", "README.md"], "scripts": {"build": "jest --silent && parcel build --no-cache", "dev": "parcel watch", "lint": "eslint src/ --report-unused-disable-directives --max-warnings 0", "test": "jest"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/clone-deep": "^4.0.4", "@types/jest": "^29.5.12", "@types/uuid": "^10.0.0", "eslint": "^9.11.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-simple-import-sort": "^12.1.1", "jest": "^29.7.0", "ts-jest": "^29.2.5"}, "dependencies": {"@types/events": "^3.0.3", "clone-deep": "^4.0.1", "events": "^3.3.0", "typed-emitter": "^2.1.0", "uuid": "^10.0.0"}}