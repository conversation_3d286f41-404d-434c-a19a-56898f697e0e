{"name": "@pipecat-ai/daily-transport", "version": "0.3.10", "license": "BSD-2-<PERSON><PERSON>", "main": "dist/index.js", "module": "dist/index.module.js", "types": "dist/index.d.ts", "source": "src/index.ts", "repository": {"type": "git", "url": "git+https://github.com/pipecat-ai/pipecat-client-web-transports.git"}, "files": ["dist", "package.json", "README.md"], "scripts": {"build": "parcel build --no-cache", "dev": "parcel watch", "lint": "eslint . --ext ts --report-unused-disable-directives --max-warnings 0"}, "devDependencies": {"@pipecat-ai/client-js": "^0.3.5", "eslint": "9.11.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-simple-import-sort": "^12.1.1"}, "peerDependencies": {"@pipecat-ai/client-js": "~0.3.5"}, "dependencies": {"@daily-co/daily-js": "^0.77.0"}, "description": "Pipecat Daily Transport Package", "author": "Daily.co", "bugs": {"url": "https://github.com/pipecat-ai/pipecat-client-web-transports/issues"}, "homepage": "https://github.com/pipecat-ai/pipecat-client-web-transports/blob/main/transports/daily-webrtc/README.md"}