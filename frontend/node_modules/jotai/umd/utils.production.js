!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("jotai/vanilla/utils"),require("jotai/react/utils")):"function"==typeof define&&define.amd?define(["exports","jotai/vanilla/utils","jotai/react/utils"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).jotaiUtils={},e.jotaiVanillaUtils,e.jotaiReactUtils)}(this,(function(e,t,i){"use strict";Object.keys(t).forEach((function(i){"default"===i||Object.prototype.hasOwnProperty.call(e,i)||Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[i]}})})),Object.keys(i).forEach((function(t){"default"===t||Object.prototype.hasOwnProperty.call(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:function(){return i[t]}})}))}));
