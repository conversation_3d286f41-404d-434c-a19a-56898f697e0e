!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@babel/template")):"function"==typeof define&&define.amd?define(["@babel/template"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).jotaiBabelPluginReactRefresh=e(t._templateBuilder)}(this,(function(t){"use strict";function e(t,e,i){void 0===i&&(i=[]);var o=[].concat(a,i);if(t.isIdentifier(e)&&o.includes(e.name))return!0;if(t.isMemberExpression(e)){var n=e.property;if(t.isIdentifier(n)&&o.includes(n.name))return!0}return!1}var a=["atom","atomFamily","atomWithDefault","atomWithObservable","atomWithReducer","atomWithReset","atomWithStorage","freezeAtom","loadable","selectAtom","splitAtom","unwrap","atomWithMachine","atomWithImmer","atomWithProxy","atomWithQuery","atomWithMutation","atomWithSubscription","atomWithStore","atomWithHash","atomWithLocation","focusAtom","atomWithValidate","validateAtoms","atomWithCache","atomWithRecoilValue"],i=t.default||t;return function(t,a){var o=t.types;return{pre:function(t){if(!t.opts.filename)throw new Error("Filename must be available")},visitor:{Program:{exit:function(t){var e=i("\n          globalThis.jotaiAtomCache = globalThis.jotaiAtomCache || {\n            cache: new Map(),\n            get(name, inst) { \n              if (this.cache.has(name)) {\n                return this.cache.get(name)\n              }\n              this.cache.set(name, inst)\n              return inst\n            },\n          }")();t.unshiftContainer("body",e)}},ExportDefaultDeclaration:function(t,n){var r=t.node;if(o.isCallExpression(r.declaration)&&e(o,r.declaration.callee,null==a?void 0:a.customAtomNames)){var l=(n.filename||"unknown")+"/defaultExport",m=i("export default globalThis.jotaiAtomCache.get(%%atomKey%%, %%atom%%)")({atomKey:o.stringLiteral(l),atom:r.declaration});t.replaceWith(m)}},VariableDeclarator:function(t,n){var r,l;if(o.isIdentifier(t.node.id)&&o.isCallExpression(t.node.init)&&e(o,t.node.init.callee,null==a?void 0:a.customAtomNames)&&(null!=(r=t.parentPath.parentPath)&&r.isProgram()||null!=(l=t.parentPath.parentPath)&&l.isExportNamedDeclaration())){var m=(n.filename||"unknown")+"/"+t.node.id.name,s=i("const %%atomIdentifier%% = globalThis.jotaiAtomCache.get(%%atomKey%%, %%atom%%)")({atomIdentifier:o.identifier(t.node.id.name),atomKey:o.stringLiteral(m),atom:t.node.init});t.parentPath.replaceWith(s)}}}}}}));
