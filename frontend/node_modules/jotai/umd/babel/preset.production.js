!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@babel/template")):"function"==typeof define&&define.amd?define(["@babel/template"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).jotaiBabelPreset=t(e._templateBuilder)}(this,(function(e){"use strict";function t(e,t,a){void 0===a&&(a=[]);var n=[].concat(i,a);if(e.isIdentifier(t)&&n.includes(t.name))return!0;if(e.isMemberExpression(t)){var o=t.property;if(e.isIdentifier(o)&&n.includes(o.name))return!0}return!1}var i=["atom","atomFamily","atomWithDefault","atomWithObservable","atomWithReducer","atomWithReset","atomWithStorage","freezeAtom","loadable","selectAtom","splitAtom","unwrap","atomWithMachine","atomWithImmer","atomWithProxy","atomWithQuery","atomWithMutation","atomWithSubscription","atomWithStore","atomWithHash","atomWithLocation","focusAtom","atomWithValidate","validateAtoms","atomWithCache","atomWithRecoilValue"],a=e.default||e;function n(e,i){var n=e.types;return{visitor:{ExportDefaultDeclaration:function(e,o){var r=e.node;if(n.isCallExpression(r.declaration)&&t(n,r.declaration.callee,null==i?void 0:i.customAtomNames)){var l=(o.filename||"unknown").replace(/\.\w+$/,""),m=l.split("/").pop();"index"===m&&(m=l.slice(0,-6).split("/").pop()||"unknown");var s=a("\n          const %%atomIdentifier%% = %%atom%%;\n          export default %%atomIdentifier%%\n          ")({atomIdentifier:n.identifier(m),atom:r.declaration});e.replaceWithMultiple(s)}},VariableDeclarator:function(e){n.isIdentifier(e.node.id)&&n.isCallExpression(e.node.init)&&t(n,e.node.init.callee,null==i?void 0:i.customAtomNames)&&e.parentPath.insertAfter(n.expressionStatement(n.assignmentExpression("=",n.memberExpression(n.identifier(e.node.id.name),n.identifier("debugLabel")),n.stringLiteral(e.node.id.name))))}}}}var o=e.default||e;function r(e,i){var a=e.types;return{pre:function(e){if(!e.opts.filename)throw new Error("Filename must be available")},visitor:{Program:{exit:function(e){var t=o("\n          globalThis.jotaiAtomCache = globalThis.jotaiAtomCache || {\n            cache: new Map(),\n            get(name, inst) { \n              if (this.cache.has(name)) {\n                return this.cache.get(name)\n              }\n              this.cache.set(name, inst)\n              return inst\n            },\n          }")();e.unshiftContainer("body",t)}},ExportDefaultDeclaration:function(e,n){var r=e.node;if(a.isCallExpression(r.declaration)&&t(a,r.declaration.callee,null==i?void 0:i.customAtomNames)){var l=(n.filename||"unknown")+"/defaultExport",m=o("export default globalThis.jotaiAtomCache.get(%%atomKey%%, %%atom%%)")({atomKey:a.stringLiteral(l),atom:r.declaration});e.replaceWith(m)}},VariableDeclarator:function(e,n){var r,l;if(a.isIdentifier(e.node.id)&&a.isCallExpression(e.node.init)&&t(a,e.node.init.callee,null==i?void 0:i.customAtomNames)&&(null!=(r=e.parentPath.parentPath)&&r.isProgram()||null!=(l=e.parentPath.parentPath)&&l.isExportNamedDeclaration())){var m=(n.filename||"unknown")+"/"+e.node.id.name,s=o("const %%atomIdentifier%% = globalThis.jotaiAtomCache.get(%%atomKey%%, %%atom%%)")({atomIdentifier:a.identifier(e.node.id.name),atomKey:a.stringLiteral(m),atom:e.node.init});e.parentPath.replaceWith(s)}}}}}return function(e,t){return{plugins:[[n,t],[r,t]]}}}));
