import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  PlayIcon,
  PencilIcon,
  EllipsisVerticalIcon,
  CheckCircleIcon,
  XCircleIcon,
  BeakerIcon
} from '@heroicons/react/24/outline';
import { Agent } from '../types';
import AgentTester from './AgentTester';
import VoiceCallInterface from './VoiceCallInterface';

interface AgentCardProps {
  agent: Agent;
  compact?: boolean;
}

const AgentCard: React.FC<AgentCardProps> = ({ agent, compact = false }) => {
  const [showTester, setShowTester] = useState(false);
  const [showCallInterface, setShowCallInterface] = useState(false);

  const handleStartSession = () => {
    setShowCallInterface(true);
  };

  const handleTestAgent = () => {
    setShowTester(true);
  };

  return (
    <div className="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 truncate">
              {agent.name}
            </h3>
            {agent.description && (
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                {agent.description}
              </p>
            )}
          </div>
          <div className="flex items-center gap-2 ml-4">
            {agent.is_active ? (
              <CheckCircleIcon className="h-5 w-5 text-green-500" title="Active" />
            ) : (
              <XCircleIcon className="h-5 w-5 text-gray-400" title="Inactive" />
            )}
            <button className="p-1 text-gray-400 hover:text-gray-600">
              <EllipsisVerticalIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Configuration */}
        {!compact && (
          <div className="space-y-2 mb-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">LLM:</span>
              <span className="font-medium">{agent.llm_provider} / {agent.llm_model}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">STT:</span>
              <span className="font-medium">{agent.stt_provider}</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">TTS:</span>
              <span className="font-medium">{agent.tts_provider}</span>
            </div>
          </div>
        )}

        {/* Tags */}
        {agent.tags && agent.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {agent.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
              >
                {tag}
              </span>
            ))}
            {agent.tags.length > 3 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                +{agent.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center gap-2">
          <button
            onClick={handleStartSession}
            disabled={!agent.is_active}
            className="btn btn-primary btn-sm flex items-center gap-1 flex-1"
          >
            <PlayIcon className="h-4 w-4" />
            Start Call
          </button>
          <button
            onClick={handleTestAgent}
            className="btn btn-secondary btn-sm flex items-center gap-1"
          >
            <BeakerIcon className="h-4 w-4" />
            Test
          </button>
          <Link
            to={`/agents/${agent.id}/edit`}
            className="btn btn-secondary btn-sm flex items-center gap-1"
          >
            <PencilIcon className="h-4 w-4" />
            Edit
          </Link>
        </div>

        {/* Footer */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Created {new Date(agent.created_at).toLocaleDateString()}</span>
            <span>Updated {new Date(agent.updated_at).toLocaleDateString()}</span>
          </div>
        </div>
      </div>

      {/* Agent Tester Modal */}
      {showTester && (
        <AgentTester
          agent={agent}
          onClose={() => setShowTester(false)}
        />
      )}

      {/* Voice Call Interface */}
      {showCallInterface && (
        <VoiceCallInterface
          agent={agent}
          onClose={() => setShowCallInterface(false)}
        />
      )}
    </div>
  );
};

export default AgentCard;
