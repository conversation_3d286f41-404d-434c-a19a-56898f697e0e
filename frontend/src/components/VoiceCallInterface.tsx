import React, { useState, useEffect } from 'react';
import { RTVIClient } from '@pipecat-ai/client-js';
import {
  RTVIClientProvider,
  RTVIClientAudio,
  RTVIClientVideo,
  useRTVIClient,
  useRTVIClientTransportState,
} from '@pipecat-ai/client-react';
import { DailyTransport } from '@pipecat-ai/daily-transport';
import { supabase } from '../services/apiClient';
import {
  PhoneIcon,
  PhoneXMarkIcon,
  MicrophoneIcon,
  SpeakerWaveIcon,
  VideoCameraIcon,
  VideoCameraSlashIcon,
} from '@heroicons/react/24/outline';
import { Agent } from '../types';

interface VoiceCallInterfaceProps {
  agent: Agent;
  onClose: () => void;
}

interface CallData {
  call_id: string;
  room_url: string;
  token: string;
  status: string;
}

const VoiceCallInterface: React.FC<VoiceCallInterfaceProps> = ({ agent, onClose }) => {
  const [isStartingCall, setIsStartingCall] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [client, setClient] = useState<RTVIClient | null>(null);

  const startCall = async () => {
    setIsStartingCall(true);
    setError(null);

    try {
      // Get authentication token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      // Create Daily transport
      const transport = new DailyTransport();

      // Get the base URL for our backend
      const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000';

      // Create RTVI client with proper configuration
      const rtviClient = new RTVIClient({
        transport,
        params: {
          baseUrl,
          endpoints: {
            connect: '/connect',
          },
          requestData: {
            agent_id: agent.id,
          },
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
          },
        },
        enableMic: true,
        enableCam: false,
        timeout: 15 * 1000,
        callbacks: {
          onConnected: () => {
            console.log('[CALLBACK] User connected');
          },
          onDisconnected: () => {
            console.log('[CALLBACK] User disconnected');
          },
          onTransportStateChanged: (state: string) => {
            console.log('[CALLBACK] State change:', state);
          },
          onBotConnected: () => {
            console.log('[CALLBACK] Bot connected');
          },
          onBotDisconnected: () => {
            console.log('[CALLBACK] Bot disconnected');
          },
          onBotReady: () => {
            console.log('[CALLBACK] Bot ready to chat!');
          },
        },
      });

      setClient(rtviClient);

      // Connect using RTVI protocol
      await rtviClient.connect();

    } catch (err: any) {
      console.error('Failed to start call:', err);
      setError(err.message || 'Failed to start call');
    } finally {
      setIsStartingCall(false);
    }
  };

  const endCall = async () => {
    try {
      if (client) {
        await client.disconnect();
        setClient(null);
      }
      onClose();

    } catch (err: any) {
      console.error('Failed to end call:', err);
      // Still close the interface even if API call fails
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Voice Call: {agent.name}
            </h3>
            <p className="text-sm text-gray-500">
              {client ? 'Connected' : 'Ready to connect'}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Agent Info */}
          <div className="mb-6 bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Agent Configuration</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">LLM:</span>
                <span className="ml-2 font-medium">{agent.llm_provider} / {agent.llm_model}</span>
              </div>
              <div>
                <span className="text-gray-500">STT:</span>
                <span className="ml-2 font-medium">{agent.stt_provider}</span>
              </div>
              <div>
                <span className="text-gray-500">TTS:</span>
                <span className="ml-2 font-medium">{agent.tts_provider}</span>
              </div>
              <div>
                <span className="text-gray-500">Language:</span>
                <span className="ml-2 font-medium">{agent.stt_language}</span>
              </div>
            </div>
          </div>

          {/* Call Interface */}
          {client ? (
            <RTVIClientProvider client={client}>
              <CallContent agent={agent} onEndCall={endCall} />
            </RTVIClientProvider>
          ) : (
            <div className="text-center py-8">
              <div className="mb-6">
                <div className="mx-auto w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <PhoneIcon className="h-10 w-10 text-blue-600" />
                </div>
                <h4 className="text-lg font-medium text-gray-900 mb-2">
                  Ready to start voice call
                </h4>
                <p className="text-gray-600">
                  Click the button below to connect with {agent.name}
                </p>
              </div>

              <button
                onClick={startCall}
                disabled={isStartingCall}
                className="flex items-center gap-2 px-6 py-3 text-white bg-green-600 rounded-lg hover:bg-green-700 disabled:opacity-50 mx-auto"
              >
                <PhoneIcon className="h-5 w-5" />
                {isStartingCall ? 'Connecting...' : 'Start Call'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Call content component that uses RTVI hooks
const CallContent: React.FC<{ agent: Agent; onEndCall: () => void }> = ({ agent, onEndCall }) => {
  const client = useRTVIClient();
  const transportState = useRTVIClientTransportState();
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(false);

  const isConnected = ['connected', 'ready'].includes(transportState);
  const isConnecting = transportState === 'connecting';

  const toggleMute = async () => {
    if (client) {
      await client.enableMic(!isMuted);
      setIsMuted(!isMuted);
    }
  };

  const toggleVideo = async () => {
    if (client) {
      await client.enableCam(!isVideoEnabled);
      setIsVideoEnabled(!isVideoEnabled);
    }
  };

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <div className="text-center">
        <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
          isConnected ? 'bg-green-100 text-green-800' :
          isConnecting ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          <div className={`w-2 h-2 rounded-full ${
            isConnected ? 'bg-green-500 animate-pulse' :
            isConnecting ? 'bg-yellow-500 animate-pulse' :
            'bg-red-500'
          }`}></div>
          {isConnected ? 'Connected' : isConnecting ? 'Connecting...' : 'Disconnected'}
        </div>
      </div>

      {/* Video Area */}
      <div className="bg-gray-900 rounded-lg aspect-video flex items-center justify-center">
        {isConnected ? (
          <RTVIClientVideo participant="bot" fit="cover" className="w-full h-full rounded-lg" />
        ) : (
          <div className="text-white text-center">
            <div className="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl font-bold">{agent.name.charAt(0)}</span>
            </div>
            <p className="text-gray-300">
              {isConnecting ? 'Connecting to agent...' : 'Waiting for connection'}
            </p>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="flex justify-center gap-4">
        <button
          onClick={toggleMute}
          disabled={!isConnected}
          className={`p-3 rounded-full ${
            isMuted ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'
          } hover:bg-gray-200 disabled:opacity-50`}
        >
          <MicrophoneIcon className="h-6 w-6" />
        </button>

        <button
          onClick={toggleVideo}
          disabled={!isConnected}
          className={`p-3 rounded-full ${
            isVideoEnabled ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
          } hover:bg-gray-200 disabled:opacity-50`}
        >
          {isVideoEnabled ? (
            <VideoCameraIcon className="h-6 w-6" />
          ) : (
            <VideoCameraSlashIcon className="h-6 w-6" />
          )}
        </button>

        <button
          onClick={onEndCall}
          className="p-3 rounded-full bg-red-100 text-red-600 hover:bg-red-200"
        >
          <PhoneXMarkIcon className="h-6 w-6" />
        </button>
      </div>

      {/* Audio Component */}
      <RTVIClientAudio />
    </div>
  );
};

export default VoiceCallInterface;
